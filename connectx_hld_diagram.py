#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
<PERSON><PERSON> đồ HLD (High Level Design) cho hệ thống Wifi Marketing ConnectX
Sử dụng thư viện diagrams để tạo sơ đồ kiến trúc hệ thống
"""

from diagrams import Diagram, Cluster, Edge
from diagrams.onprem.client import Users, Client
from diagrams.onprem.network import Internet, Nginx
from diagrams.onprem.compute import Server
from diagrams.onprem.database import MySQL, Redis
from diagrams.programming.framework import Vue, Laravel
from diagrams.programming.language import PHP, JavaScript
from diagrams.onprem.monitoring import Grafana
from diagrams.generic.device import Mobile, Tablet
from diagrams.generic.network import Router
from diagrams.aws.network import CloudFront
from diagrams.onprem.inmemory import Redis as RedisCache

def create_connectx_hld():
    """Tạo sơ đồ HLD cho hệ thống ConnectX"""
    
    with Diagram("<PERSON><PERSON> thống Wifi Marketing ConnectX - HLD", 
                 filename="connectx_hld", 
                 show=False, 
                 direction="TB",
                 graph_attr={
                     "fontsize": "16",
                     "fontname": "Arial",
                     "bgcolor": "white",
                     "pad": "0.5",
                     "nodesep": "0.8",
                     "ranksep": "1.0"
                 }):
        
        # Người dùng cuối
        with Cluster("Người dùng"):
            end_users = [
                Mobile("Điện thoại"),
                Tablet("Máy tính bảng"),
                Client("Laptop")
            ]
        
        # Quản trị viên
        with Cluster("Quản trị viên"):
            admin_users = [
                Client("Admin"),
                Client("Đại lý")
            ]
        
        # Tầng mạng
        with Cluster("Tầng mạng"):
            wifi_router = Router("WiFi Router")
            internet = Internet("Internet")
        
        # Load Balancer
        load_balancer = Nginx("Load Balancer")
        
        # Tầng ứng dụng
        with Cluster("Tầng ứng dụng"):
            
            # CMS Frontend
            with Cluster("CMS Frontend (Vue 3)"):
                cms_app = Vue("CMS Admin Panel")
                cms_features = [
                    Server("Quản lý tài khoản"),
                    Server("Quản lý thiết bị"),
                    Server("Quản lý chiến dịch"),
                    Server("Quản lý đại lý"),
                    Server("Thống kê & Báo cáo")
                ]
            
            # Captive Portal
            with Cluster("Captive Portal (Vue 3)"):
                portal_app = Vue("Captive Portal")
                portal_features = [
                    Server("Trang chào"),
                    Server("Nội dung tĩnh"),
                    Server("Nội dung động"),
                    Server("Game Vòng quay"),
                    Server("Zalo Mini App")
                ]
        
        # Tầng API
        with Cluster("Backend API (PHP Yii2)"):
            api_server = PHP("API Server")
            
            with Cluster("API Modules"):
                api_modules = [
                    Laravel("Auth Module"),
                    Laravel("Device Module"),
                    Laravel("Campaign Module"),
                    Laravel("Content Module"),
                    Laravel("Customer Module"),
                    Laravel("Analytics Module")
                ]
        
        # Tầng Cache
        with Cluster("Cache Layer"):
            redis_cache = RedisCache("Redis Cache")
            redis_session = Redis("Redis Session")
        
        # Tầng dữ liệu
        with Cluster("Tầng dữ liệu"):
            
            with Cluster("MySQL Database"):
                mysql_db = MySQL("MySQL Primary")
                
                with Cluster("Database Tables"):
                    db_tables = [
                        Server("users"),
                        Server("devices"),
                        Server("campaigns"),
                        Server("contents"),
                        Server("customers"),
                        Server("analytics")
                    ]
        
        # Monitoring
        with Cluster("Giám sát"):
            monitoring = Grafana("Monitoring & Logs")
        
        # Kết nối các thành phần
        
        # Người dùng cuối -> WiFi -> Internet -> Load Balancer
        for user in end_users:
            user >> Edge(label="WiFi", style="dashed") >> wifi_router
        
        wifi_router >> internet >> load_balancer
        
        # Admin users -> Internet -> Load Balancer
        for admin in admin_users:
            admin >> Edge(label="HTTPS", color="blue") >> internet
        
        internet >> load_balancer
        
        # Load Balancer -> Applications
        load_balancer >> Edge(label="Route", color="green") >> cms_app
        load_balancer >> Edge(label="Route", color="green") >> portal_app
        
        # Frontend -> Backend API
        cms_app >> Edge(label="REST API", color="orange") >> api_server
        portal_app >> Edge(label="REST API", color="orange") >> api_server
        
        # API Server -> Cache
        api_server >> Edge(label="Cache", color="red") >> redis_cache
        api_server >> Edge(label="Session", color="red") >> redis_session
        
        # API Server -> Database
        api_server >> Edge(label="SQL", color="purple") >> mysql_db
        
        # Monitoring connections
        api_server >> Edge(label="Logs", style="dotted") >> monitoring
        mysql_db >> Edge(label="Metrics", style="dotted") >> monitoring
        redis_cache >> Edge(label="Metrics", style="dotted") >> monitoring

def create_detailed_features_diagram():
    """Tạo sơ đồ chi tiết các tính năng của hệ thống"""
    
    with Diagram("ConnectX - Chi tiết tính năng hệ thống", 
                 filename="connectx_features", 
                 show=False, 
                 direction="LR",
                 graph_attr={
                     "fontsize": "14",
                     "fontname": "Arial",
                     "bgcolor": "white"
                 }):
        
        # Core System
        with Cluster("Hệ thống Core"):
            core_system = Server("ConnectX Core")
        
        # Authentication & Authorization
        with Cluster("Xác thực & Phân quyền"):
            auth_features = [
                Server("Quản lý tài khoản"),
                Server("Quản lý nhóm quyền"),
                Server("Đăng nhập/Đăng xuất"),
                Server("JWT Token")
            ]
        
        # Device & Campaign Management
        with Cluster("Quản lý thiết bị & Chiến dịch"):
            device_features = [
                Server("Quản lý thiết bị"),
                Server("Quản lý chiến dịch"),
                Server("Cấu hình WiFi"),
                Server("Giám sát thiết bị")
            ]
        
        # Content Management
        with Cluster("Quản lý nội dung"):
            content_features = [
                Server("Captive Portal"),
                Server("Nội dung tĩnh"),
                Server("Nội dung động"),
                Server("Game Vòng quay"),
                Server("Zalo Mini App"),
                Server("Quảng cáo")
            ]
        
        # Customer & Target Management
        with Cluster("Quản lý khách hàng"):
            customer_features = [
                Server("Quản lý khách hàng"),
                Server("Đối tượng mục tiêu"),
                Server("Phân khúc khách hàng"),
                Server("Lịch sử truy cập")
            ]
        
        # Business Management
        with Cluster("Quản lý kinh doanh"):
            business_features = [
                Server("Quản lý gói cước"),
                Server("Quản lý đại lý"),
                Server("Thanh toán"),
                Server("Hóa đơn")
            ]
        
        # Analytics & Reporting
        with Cluster("Thống kê & Báo cáo"):
            analytics_features = [
                Server("Thống kê truy cập"),
                Server("Báo cáo doanh thu"),
                Server("Phân tích hành vi"),
                Server("Dashboard")
            ]
        
        # Connections
        core_system >> auth_features
        core_system >> device_features
        core_system >> content_features
        core_system >> customer_features
        core_system >> business_features
        core_system >> analytics_features

if __name__ == "__main__":
    print("Đang tạo sơ đồ HLD cho hệ thống ConnectX...")
    
    # Tạo sơ đồ kiến trúc tổng thể
    create_connectx_hld()
    print("✓ Đã tạo sơ đồ kiến trúc tổng thể: connectx_hld.png")
    
    # Tạo sơ đồ chi tiết tính năng
    create_detailed_features_diagram()
    print("✓ Đã tạo sơ đồ chi tiết tính năng: connectx_features.png")
    
    print("\nHướng dẫn sử dụng:")
    print("1. Cài đặt thư viện: pip install diagrams")
    print("2. Chạy script: python connectx_hld_diagram.py")
    print("3. Xem kết quả trong các file PNG được tạo")
