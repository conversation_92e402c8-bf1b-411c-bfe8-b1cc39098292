# Sơ đồ HLD Hệ thống Wifi Marketing ConnectX

## <PERSON><PERSON> tả
Dự án này tạo ra các sơ đồ HLD (High Level Design) cho hệ thống Wifi Marketing ConnectX sử dụng Python và thư viện `diagrams`.

## Kiến trúc hệ thống

### C<PERSON><PERSON> thành phần chính:
1. **Frontend Layer**
   - CMS Admin Panel (Vue 3)
   - Captive Portal (Vue 3)

2. **Backend Layer**
   - API Server (PHP Yii2)
   - Authentication & Authorization
   - Business Logic Modules

3. **Data Layer**
   - MySQL Database (Primary storage)
   - Redis Cache (Caching & Session)

4. **Infrastructure**
   - Load Balancer (Nginx)
   - Monitoring & Logging

### Các module chức năng:
- Quản lý tài khoản & nhóm quyền
- Quản lý thiết bị & chiến dịch
- Quản lý captive portal & nội dung
- Quản lý đối tượng mục tiê<PERSON> & gói cước
- Quản lý đại lý & kh<PERSON>ch hàng
- Thống kê & báo cáo

## Cài đặt

### Yêu cầu hệ thống:
- Python 3.7+
- Graphviz

### Cài đặt Graphviz:

**macOS:**
```bash
brew install graphviz
```

**Ubuntu/Debian:**
```bash
sudo apt-get install graphviz
```

**Windows:**
- Tải và cài đặt từ: https://graphviz.org/download/

### Cài đặt Python dependencies:
```bash
pip install -r requirements.txt
```

## Sử dụng

### Tạo sơ đồ:
```bash
python connectx_hld_diagram.py
```

### Kết quả:
Script sẽ tạo ra 2 file PNG:
1. `connectx_hld.png` - Sơ đồ kiến trúc tổng thể
2. `connectx_features.png` - Sơ đồ chi tiết các tính năng

## Cấu trúc sơ đồ

### 1. Sơ đồ kiến trúc tổng thể (connectx_hld.png)
- Hiển thị luồng dữ liệu từ người dùng đến database
- Các layer và cluster được tổ chức rõ ràng
- Kết nối giữa các thành phần với nhãn mô tả

### 2. Sơ đồ chi tiết tính năng (connectx_features.png)
- Chi tiết các module và tính năng của hệ thống
- Mối quan hệ giữa core system và các module

## Tùy chỉnh

Bạn có thể tùy chỉnh sơ đồ bằng cách:
1. Thay đổi màu sắc của các edge
2. Thêm/bớt các thành phần
3. Điều chỉnh layout và spacing
4. Thay đổi icon và label

## Lưu ý
- Đảm bảo Graphviz đã được cài đặt đúng cách
- File PNG sẽ được tạo trong cùng thư mục với script
- Có thể mất vài giây để render sơ đồ phức tạp
